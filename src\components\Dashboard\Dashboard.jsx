import { useState, useEffect } from 'react';
import {
  User,
  LogOut,
  Plus,
  Eye,
  Trash2,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  Calendar,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  FileText,
  Coins,
  TrendingUp,
  Users
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import useDashboard from '../../hooks/useDashboard';
import { useNotifications } from '../../hooks/useNotifications';
import DeleteResultModal from './DeleteResultModal';
import ConnectionStatus from './ConnectionStatus';

export default function Dashboard() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, job: null });

  // WebSocket notifications
  const { notifications, clearNotification } = useNotifications({
    onAnalysisComplete: () => {
      refreshData();
    },
    onAnalysisFailed: () => {
      refreshData();
    }
  });

  const {
    data: { stats, results, tokenBalance, pagination },
    loading: isLoading,
    error,
    deleteResult,
    refreshData,
    setError
  } = useDashboard(currentPage, 10);

  // Debug logging for dashboard data
  useEffect(() => {
    console.log('🎯 Dashboard Data Updated:', {
      resultsCount: results.length,
      stats,
      tokenBalance,
      currentPage,
      isLoading,
      error: error || 'none'
    });
  }, [results.length, stats, tokenBalance, currentPage, isLoading, error]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const configs = {
      completed: {
        icon: CheckCircle,
        classes: 'bg-green-100 text-green-800 border-green-200',
        label: 'Completed'
      },
      processing: {
        icon: Clock,
        classes: 'bg-blue-100 text-blue-800 border-blue-200',
        label: 'Processing'
      },
      pending: {
        icon: Clock,
        classes: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        label: 'Pending'
      },
      failed: {
        icon: AlertCircle,
        classes: 'bg-red-100 text-red-800 border-red-200',
        label: 'Failed'
      }
    };

    const config = configs[status] || configs.completed;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded-full border ${config.classes}`}>
        <Icon className="h-3 w-3" />
        <span>{config.label}</span>
      </span>
    );
  };

  const handleViewResult = (result) => {
    if (result.status === 'completed' && result.id) {
      navigate(`/results/${result.id}`);
    } else {
      console.log('Result not completed or no ID available:', result);
    }
  };

  const handleDeleteResult = (result) => {
    setDeleteModal({ isOpen: true, job: result });
  };

  const handleDeleteConfirmed = async (deletedResult) => {
    const idToDelete = deletedResult.id || deletedResult.result_id || deletedResult.job_id;
    const result = await deleteResult(idToDelete);
    if (result.success) {
      setDeleteModal({ isOpen: false, job: null });
    } else {
      setError(result.error);
    }
  };

  const handleCloseDeleteModal = () => {
    setDeleteModal({ isOpen: false, job: null });
  };

  const handleLogout = async () => {
    try {
      await apiService.logout();
    } catch (err) {
      // Logout error handled silently
    } finally {
      logout();
      navigate('/auth');
    }
  };

  const handleNewAssessment = () => {
    navigate('/assessment');
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-indigo-100 rounded-xl">
                <BarChart3 className="h-8 w-8 text-indigo-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-sm text-gray-600 flex items-center space-x-1">
                  <span>Welcome back,</span>
                  <span className="font-medium text-indigo-600">{user?.email || 'User'}</span>
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <ConnectionStatus />

              {/* Debug Assessment Button - Only show in development */}
              {import.meta.env.DEV && (
                <button
                  onClick={() => navigate('/assessment?debug=true')}
                  className="flex items-center space-x-2 px-4 py-2 text-white bg-orange-500 rounded-lg hover:bg-orange-600 transition-colors"
                  title="Debug Assessment (Auto-filled)"
                >
                  <span className="text-sm">🔧</span>
                  <span className="hidden sm:inline text-sm">Debug Assessment</span>
                </button>
              )}

              <button
                onClick={handleProfile}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">Profile</span>
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <LogOut className="h-4 w-4" />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="fixed top-4 right-4 z-50 space-y-2">
          {notifications.slice(0, 3).map((notification) => (
            <div
              key={notification.id}
              className={`max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ${
                notification.type === 'success' ? 'border-l-4 border-green-500' : 'border-l-4 border-red-500'
              }`}
            >
              <div className="p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {notification.type === 'success' ? (
                      <CheckCircle className="h-6 w-6 text-green-400" />
                    ) : (
                      <AlertCircle className="h-6 w-6 text-red-400" />
                    )}
                  </div>
                  <div className="ml-3 w-0 flex-1 pt-0.5">
                    <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                    <p className="mt-1 text-sm text-gray-500">{notification.message}</p>
                  </div>
                  <div className="ml-4 flex-shrink-0 flex">
                    <button
                      className="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      onClick={() => clearNotification(notification.id)}
                    >
                      <span className="sr-only">Close</span>
                      <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Modern Stats Cards with Gradient Backgrounds */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Results */}
          <div className="relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
            <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white bg-opacity-5 rounded-full -ml-8 -mb-8"></div>
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div className="text-white text-opacity-60 text-sm font-medium">
                  {isLoading ? '...' : '+12%'}
                </div>
              </div>
              <div>
                <p className="text-white text-opacity-80 text-sm font-medium mb-1">Total Results</p>
                {isLoading ? (
                  <div className="h-8 bg-white bg-opacity-20 rounded w-16 animate-pulse"></div>
                ) : (
                  <p className="text-3xl font-bold text-white">{stats.total_results || 0}</p>
                )}
              </div>
            </div>
          </div>

          {/* Total Jobs */}
          <div className="relative overflow-hidden bg-gradient-to-br from-purple-500 to-purple-600 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
            <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white bg-opacity-5 rounded-full -ml-8 -mb-8"></div>
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div className="text-white text-opacity-60 text-sm font-medium">
                  {isLoading ? '...' : '+8%'}
                </div>
              </div>
              <div>
                <p className="text-white text-opacity-80 text-sm font-medium mb-1">Total Jobs</p>
                {isLoading ? (
                  <div className="h-8 bg-white bg-opacity-20 rounded w-16 animate-pulse"></div>
                ) : (
                  <p className="text-3xl font-bold text-white">{stats.total_jobs || 0}</p>
                )}
              </div>
            </div>
          </div>

          {/* Completed Assessments */}
          <div className="relative overflow-hidden bg-gradient-to-br from-emerald-500 to-emerald-600 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
            <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white bg-opacity-5 rounded-full -ml-8 -mb-8"></div>
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div className="text-white text-opacity-60 text-sm font-medium">
                  {isLoading ? '...' : '+15%'}
                </div>
              </div>
              <div>
                <p className="text-white text-opacity-80 text-sm font-medium mb-1">Completed</p>
                {isLoading ? (
                  <div className="h-8 bg-white bg-opacity-20 rounded w-16 animate-pulse"></div>
                ) : (
                  <p className="text-3xl font-bold text-white">{stats.completed_assessments || 0}</p>
                )}
              </div>
            </div>
          </div>

          {/* Token Balance */}
          <div className="relative overflow-hidden bg-gradient-to-br from-amber-500 to-amber-600 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
            <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white bg-opacity-5 rounded-full -ml-8 -mb-8"></div>
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                  <Coins className="h-6 w-6 text-white" />
                </div>
                <div className="text-white text-opacity-60 text-sm font-medium">
                  {isLoading ? '...' : 'Active'}
                </div>
              </div>
              <div>
                <p className="text-white text-opacity-80 text-sm font-medium mb-1">Token Balance</p>
                {isLoading ? (
                  <div className="h-8 bg-white bg-opacity-20 rounded w-16 animate-pulse"></div>
                ) : (
                  <p className="text-3xl font-bold text-white">{tokenBalance || 0}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Modern Archetype Distribution */}
        {stats.archetype_distribution && Object.keys(stats.archetype_distribution).length > 0 && (
          <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 mb-8 backdrop-blur-sm">
            <div className="flex items-center space-x-4 mb-8">
              <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Archetype Distribution</h2>
                <p className="text-gray-600">Your personality archetype breakdown</p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(stats.archetype_distribution).map(([archetype, count], index) => {
                const colors = [
                  'from-rose-400 to-pink-500',
                  'from-blue-400 to-indigo-500',
                  'from-green-400 to-emerald-500',
                  'from-yellow-400 to-orange-500',
                  'from-purple-400 to-violet-500',
                  'from-cyan-400 to-teal-500'
                ];
                const colorClass = colors[index % colors.length];

                return (
                  <div key={archetype} className="relative group">
                    <div className={`bg-gradient-to-br ${colorClass} p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1`}>
                      <div className="text-center">
                        <p className="text-white text-opacity-90 text-sm font-medium mb-2">{archetype}</p>
                        <p className="text-3xl font-bold text-white">{count}</p>
                      </div>
                      <div className="absolute inset-0 bg-white bg-opacity-0 group-hover:bg-opacity-10 rounded-xl transition-all duration-300"></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Modern Recent Activity */}
        {stats.recent_activity && stats.recent_activity.length > 0 && (
          <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 mb-8">
            <div className="flex items-center space-x-4 mb-8">
              <div className="p-3 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl shadow-lg">
                <Activity className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Recent Activity</h2>
                <p className="text-gray-600">Your latest assessment activities</p>
              </div>
            </div>
            <div className="space-y-4">
              {stats.recent_activity.map((activity, index) => (
                <div key={activity.id} className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-300 hover:border-indigo-200">
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
                  <div className="relative flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-sm">
                        <FileText className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-lg font-semibold text-gray-900 mb-1">{activity.archetype}</p>
                        <p className="text-sm text-gray-500 flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(activity.created_at)}</span>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(activity.status)}
                      <div className="w-2 h-2 bg-indigo-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Assessment Results */}
        <div className="bg-white shadow-sm rounded-xl border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <FileText className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Assessment Results</h2>
                  <p className="text-sm text-gray-500">View your assessment results and analysis</p>
                </div>
              </div>

              <button
                onClick={handleNewAssessment}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm"
              >
                <Plus className="h-4 w-4" />
                <span>New Assessment</span>
              </button>
            </div>
          </div>

          {results.length === 0 ? (
            <div className="text-center py-16">
              <div className="p-4 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-4">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No assessments yet</h3>
              <p className="text-gray-500 mb-6">Your assessment results will appear here once completed.</p>
              <button
                onClick={handleNewAssessment}
                className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Start Your First Assessment</span>
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-100">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <FileText className="h-4 w-4" />
                        <span>Result ID</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Date & Time</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <Activity className="h-4 w-4" />
                        <span>Status</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {results.map((result) => (
                    <tr key={result.id} className="hover:bg-gray-50 transition-colors group">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-mono text-gray-900">
                          {result.id}
                        </div>
                        <div className="text-xs text-gray-500">
                          {result.assessment_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatDate(result.created_at)}
                        </div>
                        {result.updated_at !== result.created_at && (
                          <div className="text-xs text-gray-500">
                            Updated: {formatDate(result.updated_at)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(result.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          {result.status === 'completed' && result.id ? (
                            <button
                              onClick={() => handleViewResult(result)}
                              className="flex items-center space-x-1 text-indigo-600 hover:text-indigo-800 font-medium text-sm transition-colors hover:bg-indigo-50 px-2 py-1 rounded"
                            >
                              <Eye className="h-4 w-4" />
                              <span className="hidden sm:inline">View Results</span>
                              <span className="sm:hidden">View</span>
                            </button>
                          ) : result.status === 'processing' ? (
                            <div className="flex items-center space-x-1 text-blue-600">
                              <div className="h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                              <span className="text-sm hidden sm:inline">Processing...</span>
                              <span className="text-sm sm:hidden">Processing</span>
                            </div>
                          ) : result.status === 'pending' ? (
                            <div className="flex items-center space-x-1 text-yellow-600">
                              <Clock className="h-4 w-4" />
                              <span className="text-sm hidden sm:inline">Pending</span>
                              <span className="text-sm sm:hidden">Pending</span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Failed</span>
                          )}

                          <button
                            onClick={() => handleDeleteResult(result)}
                            className="flex items-center space-x-1 text-red-600 hover:text-red-800 font-medium text-sm transition-colors hover:bg-red-50 px-2 py-1 rounded"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="hidden sm:inline">Delete</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-100 bg-gray-50">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * pagination.limit) + 1} to {Math.min(currentPage * pagination.limit, pagination.total)} of {pagination.total} results
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      const newPage = Math.max(currentPage - 1, 1);
                      setCurrentPage(newPage);
                    }}
                    disabled={currentPage === 1}
                    className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(pagination.totalPages, 5) }, (_, i) => (
                      <button
                        key={i + 1}
                        onClick={() => {
                          setCurrentPage(i + 1);
                        }}
                        className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                          currentPage === i + 1
                            ? 'bg-indigo-600 text-white'
                            : 'text-gray-600 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={() => {
                      const newPage = Math.min(currentPage + 1, pagination.totalPages);
                      setCurrentPage(newPage);
                    }}
                    disabled={currentPage === pagination.totalPages}
                    className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <span>Next</span>
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Result Modal */}
      <DeleteResultModal
        isOpen={deleteModal.isOpen}
        onClose={handleCloseDeleteModal}
        result={deleteModal.job}
        onDeleted={handleDeleteConfirmed}
      />
    </div>
  );
}
