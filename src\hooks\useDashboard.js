import { useState, useEffect, useCallback, useRef } from 'react';
import apiService from '../services/apiService';

/**
 * Custom hook for managing dashboard data using the new APIs:
 * - GET /api/archive/stats - User statistics
 * - GET /api/archive/results - User results with pagination
 * - GET /api/auth/token-balance - Token balance
 */
export const useDashboard = (currentPage = 1, limit = 10) => {
  const isMountedRef = useRef(true);
  const isFetchingRef = useRef(false);

  const [data, setData] = useState({
    stats: {
      total_results: 0,
      total_jobs: 0,
      completed_assessments: 0,
      archetype_distribution: {},
      recent_activity: []
    },
    results: [],
    tokenBalance: 0,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchDashboardData = useCallback(async () => {
    if (!isMountedRef.current || isFetchingRef.current) {
      return;
    }

    isFetchingRef.current = true;
    console.log('🚀 Fetching dashboard data with new APIs...');

    try {
      setLoading(true);
      setError('');

      // Fetch data from the new APIs in parallel
      const [statsResponse, resultsResponse, tokenBalanceResponse] = await Promise.all([
        apiService.getStats(),
        apiService.getUserResults({ page: currentPage, limit }),
        apiService.getTokenBalance()
      ]);

      console.log('✅ API responses received:', {
        stats: statsResponse,
        results: resultsResponse,
        tokenBalance: tokenBalanceResponse
      });

      if (!isMountedRef.current) return;

      // Process stats data
      const statsData = statsResponse?.data || {
        total_results: 0,
        total_jobs: 0,
        completed_assessments: 0,
        archetype_distribution: {},
        recent_activity: []
      };

      // Process results data (new structure from /api/archive/results)
      const resultsData = resultsResponse?.data?.results || [];
      const resultsPagination = resultsResponse?.data?.pagination || {};

      // Process token balance
      const tokenBalance = tokenBalanceResponse?.data?.tokenBalance || 0;

      // Calculate pagination from the new response structure
      const total = resultsPagination.total || 0;
      const totalPages = resultsPagination.totalPages || Math.ceil(total / limit);

      const newData = {
        stats: statsData,
        results: resultsData,
        tokenBalance,
        pagination: {
          page: resultsPagination.page || currentPage,
          limit: resultsPagination.limit || limit,
          total,
          totalPages,
          hasNext: currentPage < totalPages,
          hasPrev: currentPage > 1
        }
      };

      console.log('💾 Setting new dashboard data:', newData);
      setData(newData);

    } catch (err) {
      console.error('❌ Error fetching dashboard data:', err);
      if (isMountedRef.current) {
        setError(err.response?.data?.message || 'Failed to load dashboard data');
      }
    } finally {
      isFetchingRef.current = false;
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [currentPage, limit]);

  const deleteResult = useCallback(async (resultId) => {
    try {
      // Use the deleteResult API to delete the result
      const response = await apiService.deleteResult(resultId);
      if (response.success) {
        // Remove from local state
        setData(prevData => ({
          ...prevData,
          results: prevData.results.filter(result => result.id !== resultId)
        }));

        // Refresh data to get updated stats
        await fetchDashboardData();
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to delete result';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  useEffect(() => {
    fetchDashboardData();

    return () => {
      isMountedRef.current = false;
      isFetchingRef.current = false;
    };
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    deleteResult,
    refreshData,
    setError
  };
};

export default useDashboard;
